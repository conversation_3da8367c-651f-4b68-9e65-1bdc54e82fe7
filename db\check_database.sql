-- Check Database Status Script
-- Run this in phpMyAdmin to see what tables exist

-- Check if pets table exists
SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN 'pets table EXISTS' 
    ELSE 'pets table MISSING' 
  END as pets_status
FROM information_schema.tables 
WHERE table_schema = 'rainbow_paws' AND table_name = 'pets';

-- List all tables in the database
SELECT table_name as 'All Tables in Database'
FROM information_schema.tables 
WHERE table_schema = 'rainbow_paws'
ORDER BY table_name;

-- Check users table columns
SELECT column_name as 'Users Table Columns'
FROM information_schema.columns 
WHERE table_schema = 'rainbow_paws' AND table_name = 'users'
ORDER BY ordinal_position;

-- Check if admin user exists
SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN CONCAT('Admin user EXISTS - Count: ', COUNT(*)) 
    ELSE 'Admin user MISSING' 
  END as admin_status
FROM users 
WHERE role = 'admin';
