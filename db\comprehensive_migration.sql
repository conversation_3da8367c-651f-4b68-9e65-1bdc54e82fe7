-- Comprehensive Migration Script for Rainbow Paws Database
-- This script fixes all database schema issues and column mismatches
-- Run this in phpMyAdmin to fix your database

-- =====================================================
-- 1. ADD MISSING PETS TABLE
-- =====================================================

-- Check if pets table exists, if not create it
CREATE TABLE IF NOT EXISTS `pets` (
  `pet_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `species` varchar(100) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `age` varchar(50) DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `photo_path` varchar(255) DEFAULT NULL,
  `special_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`pet_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 2. FIX USERS TABLE COLUMN MISMATCHES
-- =====================================================

-- Add missing columns to users table (check if they exist first)
-- Add phone_number column
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND column_name = 'phone_number') > 0,
  'SELECT "phone_number column exists"',
  'ALTER TABLE `users` ADD COLUMN `phone_number` varchar(20) DEFAULT NULL AFTER `phone`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add sex column
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND column_name = 'sex') > 0,
  'SELECT "sex column exists"',
  'ALTER TABLE `users` ADD COLUMN `sex` enum(''Male'',''Female'',''Other'') DEFAULT NULL AFTER `gender`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add user_type column
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND column_name = 'user_type') > 0,
  'SELECT "user_type column exists"',
  'ALTER TABLE `users` ADD COLUMN `user_type` varchar(50) DEFAULT NULL AFTER `role`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add sms_notifications column
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND column_name = 'sms_notifications') > 0,
  'SELECT "sms_notifications column exists"',
  'ALTER TABLE `users` ADD COLUMN `sms_notifications` tinyint(1) DEFAULT 1 AFTER `updated_at`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add email_notifications column
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND column_name = 'email_notifications') > 0,
  'SELECT "email_notifications column exists"',
  'ALTER TABLE `users` ADD COLUMN `email_notifications` tinyint(1) DEFAULT 1 AFTER `sms_notifications`'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Copy phone to phone_number if phone_number is empty
UPDATE `users` SET `phone_number` = `phone` WHERE `phone_number` IS NULL AND `phone` IS NOT NULL;

-- Copy gender to sex if sex is empty  
UPDATE `users` SET `sex` = `gender` WHERE `sex` IS NULL AND `gender` IS NOT NULL;

-- Set user_type based on role for backward compatibility
UPDATE `users` SET `user_type` = CASE 
  WHEN `role` = 'fur_parent' THEN 'user'
  WHEN `role` = 'admin' THEN 'admin'
  WHEN `role` = 'business' THEN 'business'
  ELSE 'user'
END WHERE `user_type` IS NULL;

-- =====================================================
-- 3. ENSURE ADMIN_PROFILES TABLE EXISTS
-- =====================================================

CREATE TABLE IF NOT EXISTS `admin_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `admin_role` varchar(50) DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 4. ENSURE ESSENTIAL ADMIN USER EXISTS
-- =====================================================

-- Insert admin user if it doesn't exist
INSERT IGNORE INTO `users` (`user_id`, `email`, `password`, `first_name`, `last_name`, `phone`, `address`, `gender`, `profile_picture`, `role`, `status`, `is_verified`, `is_otp_verified`, `last_login`, `created_at`, `updated_at`, `sms_notifications`, `email_notifications`, `user_type`, `phone_number`, `sex`) VALUES
(1, '<EMAIL>', '$2b$10$/TMOT7juT/ytAoRAOjjP.uOu1ZpQiMYRVnvQP9UJLv/KC2CfLaxTe', 'Admin', 'System', NULL, NULL, NULL, NULL, 'admin', 'active', 1, 1, NULL, NOW(), NOW(), 1, 1, 'admin', NULL, NULL);

-- Insert admin profile if it doesn't exist
INSERT IGNORE INTO `admin_profiles` (`id`, `user_id`, `username`, `full_name`, `admin_role`, `created_at`, `updated_at`) VALUES
(1, 1, 'admin', 'Admin System', 'admin', NOW(), NOW());

-- =====================================================
-- 5. ENSURE SERVICE_BOOKINGS TABLE EXISTS
-- =====================================================

CREATE TABLE IF NOT EXISTS `service_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `provider_id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL,
  `pet_name` varchar(255) DEFAULT NULL,
  `pet_type` varchar(100) DEFAULT NULL,
  `pet_breed` varchar(255) DEFAULT NULL,
  `pet_age` varchar(50) DEFAULT NULL,
  `pet_weight` decimal(8,2) DEFAULT NULL,
  `pet_gender` varchar(50) DEFAULT NULL,
  `pet_special_notes` text DEFAULT NULL,
  `booking_date` date DEFAULT NULL,
  `booking_time` time DEFAULT NULL,
  `status` enum('pending','confirmed','in_progress','completed','cancelled') DEFAULT 'pending',
  `total_amount` decimal(10,2) DEFAULT NULL,
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `special_requests` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_service_bookings_user` (`user_id`),
  KEY `idx_service_bookings_provider` (`provider_id`),
  KEY `idx_service_bookings_package` (`package_id`),
  KEY `idx_service_bookings_status` (`status`),
  KEY `idx_service_bookings_date` (`booking_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 6. ADD INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Add indexes to users table (check if they exist first)
-- Add role index
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND index_name = 'idx_users_role') > 0,
  'SELECT "idx_users_role index exists"',
  'ALTER TABLE `users` ADD INDEX `idx_users_role` (`role`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add status index
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND index_name = 'idx_users_status') > 0,
  'SELECT "idx_users_status index exists"',
  'ALTER TABLE `users` ADD INDEX `idx_users_status` (`status`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add user_type index
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
   WHERE table_name = 'users'
   AND table_schema = DATABASE()
   AND index_name = 'idx_users_user_type') > 0,
  'SELECT "idx_users_user_type index exists"',
  'ALTER TABLE `users` ADD INDEX `idx_users_user_type` (`user_type`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 7. UPDATE AUTO_INCREMENT VALUES
-- =====================================================

-- Ensure AUTO_INCREMENT is set properly
ALTER TABLE `users` AUTO_INCREMENT = 2;
ALTER TABLE `admin_profiles` AUTO_INCREMENT = 2;
ALTER TABLE `pets` AUTO_INCREMENT = 1;
ALTER TABLE `service_bookings` AUTO_INCREMENT = 1;
