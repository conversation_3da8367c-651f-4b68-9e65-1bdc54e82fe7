-- Simple Migration Script for Rainbow Paws Database
-- Run this in phpMyAdmin to fix your database issues
-- This script is safe to run multiple times

-- =====================================================
-- 1. CREATE PETS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `pets` (
  `pet_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `species` varchar(100) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `age` varchar(50) DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `photo_path` varchar(255) DEFAULT NULL,
  `special_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`pet_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 2. ADD MISSING COLUMNS TO USERS TABLE
-- =====================================================

-- Add phone_number column (duplicate of phone for API compatibility)
ALTER TABLE `users` ADD COLUMN `phone_number` varchar(20) DEFAULT NULL;

-- Add sex column (duplicate of gender for API compatibility)  
ALTER TABLE `users` ADD COLUMN `sex` enum('Male','Female','Other') DEFAULT NULL;

-- Add user_type column for backward compatibility
ALTER TABLE `users` ADD COLUMN `user_type` varchar(50) DEFAULT NULL;

-- Add notification preference columns
ALTER TABLE `users` ADD COLUMN `sms_notifications` tinyint(1) DEFAULT 1;
ALTER TABLE `users` ADD COLUMN `email_notifications` tinyint(1) DEFAULT 1;

-- =====================================================
-- 3. UPDATE DATA FOR COMPATIBILITY
-- =====================================================

-- Copy phone to phone_number
UPDATE `users` SET `phone_number` = `phone` WHERE `phone_number` IS NULL;

-- Copy gender to sex
UPDATE `users` SET `sex` = `gender` WHERE `sex` IS NULL;

-- Set user_type based on role
UPDATE `users` SET `user_type` = CASE 
  WHEN `role` = 'fur_parent' THEN 'user'
  WHEN `role` = 'admin' THEN 'admin'
  WHEN `role` = 'business' THEN 'business'
  ELSE 'user'
END WHERE `user_type` IS NULL;

-- =====================================================
-- 4. ENSURE ADMIN USER EXISTS
-- =====================================================

-- Insert admin user if it doesn't exist
INSERT IGNORE INTO `users` (
  `user_id`, `email`, `password`, `first_name`, `last_name`, 
  `role`, `status`, `is_verified`, `is_otp_verified`, 
  `created_at`, `updated_at`, `sms_notifications`, `email_notifications`, 
  `user_type`, `phone_number`, `sex`
) VALUES (
  1, '<EMAIL>', 
  '$2b$10$/TMOT7juT/ytAoRAOjjP.uOu1ZpQiMYRVnvQP9UJLv/KC2CfLaxTe', 
  'Admin', 'System', 'admin', 'active', 1, 1, 
  NOW(), NOW(), 1, 1, 'admin', NULL, NULL
);

-- Insert admin profile if it doesn't exist
INSERT IGNORE INTO `admin_profiles` (
  `id`, `user_id`, `username`, `full_name`, `admin_role`, 
  `created_at`, `updated_at`
) VALUES (
  1, 1, 'admin', 'Admin System', 'admin', NOW(), NOW()
);

-- =====================================================
-- 5. ADD USEFUL INDEXES
-- =====================================================

-- Add indexes for better performance (ignore errors if they exist)
ALTER TABLE `users` ADD INDEX `idx_users_role` (`role`);
ALTER TABLE `users` ADD INDEX `idx_users_status` (`status`);
ALTER TABLE `users` ADD INDEX `idx_users_user_type` (`user_type`);

-- =====================================================
-- 6. VERIFY SETUP
-- =====================================================

-- Show table status
SELECT 'Migration completed successfully' as status;
SELECT COUNT(*) as pets_table_ready FROM information_schema.tables WHERE table_name = 'pets' AND table_schema = DATABASE();
SELECT COUNT(*) as admin_users FROM users WHERE role = 'admin';
SELECT COUNT(*) as total_users FROM users;
