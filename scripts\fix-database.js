#!/usr/bin/env node

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

async function fixDatabase() {
  let connection;
  
  try {
    console.log('🔧 Starting database migration...');
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'rainbow_paws',
      port: parseInt(process.env.DB_PORT || '3306'),
    });

    console.log('✅ Connected to database');

    // Check if pets table exists
    console.log('🔍 Checking if pets table exists...');
    const [tableCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'pets'
    `, [process.env.DB_NAME || 'rainbow_paws']);

    if (tableCheck[0].count > 0) {
      console.log('⚠️  Pets table already exists, dropping it first...');
      await connection.execute('DROP TABLE IF EXISTS `pets`');
    }

    // Create pets table
    console.log('🏗️  Creating pets table...');
    await connection.execute(`
      CREATE TABLE \`pets\` (
        \`pet_id\` int(11) NOT NULL AUTO_INCREMENT,
        \`user_id\` varchar(255) NOT NULL,
        \`name\` varchar(255) NOT NULL,
        \`species\` varchar(100) NOT NULL,
        \`breed\` varchar(255) DEFAULT NULL,
        \`gender\` varchar(50) DEFAULT NULL,
        \`age\` varchar(50) DEFAULT NULL,
        \`weight\` decimal(8,2) DEFAULT NULL,
        \`photo_path\` varchar(255) DEFAULT NULL,
        \`special_notes\` text DEFAULT NULL,
        \`created_at\` timestamp NOT NULL DEFAULT current_timestamp(),
        \`updated_at\` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (\`pet_id\`),
        KEY \`user_id\` (\`user_id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    `);

    console.log('✅ Pets table created successfully');

    // Check and add missing columns to users table
    console.log('🔍 Checking users table columns...');
    
    const [columns] = await connection.execute(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = ? AND table_name = 'users'
    `, [process.env.DB_NAME || 'rainbow_paws']);

    const existingColumns = columns.map(row => row.column_name);
    
    // Add missing columns
    const columnsToAdd = [
      { name: 'phone_number', sql: 'ADD COLUMN `phone_number` varchar(20) DEFAULT NULL' },
      { name: 'sex', sql: 'ADD COLUMN `sex` enum(\'Male\',\'Female\',\'Other\') DEFAULT NULL' },
      { name: 'user_type', sql: 'ADD COLUMN `user_type` varchar(50) DEFAULT NULL' },
      { name: 'sms_notifications', sql: 'ADD COLUMN `sms_notifications` tinyint(1) DEFAULT 1' },
      { name: 'email_notifications', sql: 'ADD COLUMN `email_notifications` tinyint(1) DEFAULT 1' }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding ${column.name} column to users table...`);
        try {
          await connection.execute(`ALTER TABLE \`users\` ${column.sql}`);
          console.log(`✅ Added ${column.name} column`);
        } catch (error) {
          console.log(`⚠️  Column ${column.name} might already exist: ${error.message}`);
        }
      } else {
        console.log(`✅ Column ${column.name} already exists`);
      }
    }

    // Update data for compatibility
    console.log('🔄 Updating data for API compatibility...');
    
    // Copy phone to phone_number
    await connection.execute(`
      UPDATE \`users\` 
      SET \`phone_number\` = \`phone\` 
      WHERE \`phone_number\` IS NULL AND \`phone\` IS NOT NULL
    `);

    // Copy gender to sex
    await connection.execute(`
      UPDATE \`users\` 
      SET \`sex\` = \`gender\` 
      WHERE \`sex\` IS NULL AND \`gender\` IS NOT NULL
    `);

    // Set user_type based on role
    await connection.execute(`
      UPDATE \`users\` 
      SET \`user_type\` = CASE 
        WHEN \`role\` = 'fur_parent' THEN 'user'
        WHEN \`role\` = 'admin' THEN 'admin'
        WHEN \`role\` = 'business' THEN 'business'
        ELSE 'user'
      END 
      WHERE \`user_type\` IS NULL
    `);

    console.log('✅ Data updated successfully');

    // Ensure admin user exists
    console.log('👤 Checking admin user...');
    const [adminCheck] = await connection.execute(`
      SELECT COUNT(*) as count FROM \`users\` WHERE \`role\` = 'admin'
    `);

    if (adminCheck[0].count === 0) {
      console.log('➕ Creating admin user...');
      await connection.execute(`
        INSERT INTO \`users\` (
          \`user_id\`, \`email\`, \`password\`, \`first_name\`, \`last_name\`, 
          \`role\`, \`status\`, \`is_verified\`, \`is_otp_verified\`, 
          \`created_at\`, \`updated_at\`, \`sms_notifications\`, \`email_notifications\`, 
          \`user_type\`
        ) VALUES (
          1, '<EMAIL>', 
          '$2b$10$/TMOT7juT/ytAoRAOjjP.uOu1ZpQiMYRVnvQP9UJLv/KC2CfLaxTe', 
          'Admin', 'System', 'admin', 'active', 1, 1, 
          NOW(), NOW(), 1, 1, 'admin'
        )
      `);
      console.log('✅ Admin user created');
    } else {
      console.log('✅ Admin user already exists');
    }

    // Verify everything is working
    console.log('🔍 Verifying migration...');
    
    const [petsTableCheck] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'pets'
    `, [process.env.DB_NAME || 'rainbow_paws']);

    const [petsCount] = await connection.execute('SELECT COUNT(*) as count FROM pets');
    
    console.log('✅ Migration completed successfully!');
    console.log(`📊 Pets table exists: ${petsTableCheck[0].count > 0 ? 'YES' : 'NO'}`);
    console.log(`📊 Pets count: ${petsCount[0].count}`);
    console.log(`📊 Admin users: ${adminCheck[0].count}`);
    
    console.log('\n🎉 Database is now ready! You can restart your application.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration
fixDatabase();
